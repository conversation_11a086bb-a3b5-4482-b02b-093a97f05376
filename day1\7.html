<header>
  <h1>my Website</h1>
  <link rel="stylesheet" type="text/css" href="style.css" />
</header>
<nav>Home<a /> | About<a /> | Contact<a /></nav>

<main></main>
<h1>The HTML</h1>
<h2>1.1 Introduction of HTML</h2>

<p>
  Great! You're all set to create your first HTML file, and view your first web
  page. In order to get started, make sure you have a text editor and a web
  browser installed on your computer. These tools will be your best friends
  throughout this journey. Once you have them ready, follow these three steps:
</p>

<ol>
  <li>Step One: Create an HTML file</li>
  <p>
    Open your text editor and create a new file. It's time to give life to your
    web page! Save the file with a memorable name and make sure to use the .html
    extension, such as sample.html. This tells the computer that it's an HTML
    file.
  </p>
  <li>Step Two: Write the HTML code</li>
  <p>
    Here comes the exciting part! Inside your HTML file, you'll start by adding
    the essential structure of an HTML document. We've got you covered with the
    code you need. Just copy the following:
  </p>
</ol>


  <button type="button" class="btn">Learn More</button>

  <footer>
    <p>&copy; 2023 My Website. All rights reserved.</p>
  </footer>

