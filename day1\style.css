body,
.login-bg {
  background-color: #e3f2fd;
  font-family: Arial, Helvetica, sans-serif;
  min-height: 100vh;
  margin: 0;
}

.login-container {
  background: #fff;
  max-width: 350px;
  margin: 60px auto;
  padding: 30px 40px 25px 40px;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
}
.login-container h2 {
  text-align: center;
  margin-bottom: 24px;
  color: #333;
}
.input-group {
  margin-bottom: 18px;
  position: relative;
}
.input-group label {
  display: block;
  margin-bottom: 6px;
  color: #333;
  font-weight: 500;
}
.input-group input {
  width: 85%;
  padding: 10px 36px 10px 12px;
  border: 1px solid #bdbdbd;
  border-radius: 6px;
  font-size: 15px;
  outline: none;
  transition: border 0.2s;
  background: #f7f7f7;
}
.input-group input:focus {
  border: 1.5px solid #4caf50;
  background: #fff;
}
.input-group .icon {
  position: absolute;
  right: 100px;
  top: 36px;
  transform: translateY(-50%);
  color: #aaa;
  font-size: 18px;
  pointer-events: none;
}
.login-btn {
  width: 100%;
  background: #4caf50;
  color: #fff;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: background 0.2s;
}
.login-btn:hover {
  background: #388e3c;
}
.login-footer {
  text-align: center;
  margin-top: 18px;
  font-size: 14px;
}
.login-footer a {
  color: #4caf50;
  text-decoration: none;
}
.login-footer a:hover {
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 500px) {
  .login-container {
    max-width: 95vw;
    padding: 18px 8vw 18px 8vw;
  }
  .input-group input {
    font-size: 14px;
    padding: 10px 32px 10px 10px;
  }
  .login-btn {
    font-size: 15px;
    padding: 10px;
  }
}

h1 {
  color: navy;
  text-align: center;
}
p {
  font-size: 14px;
  line-height: 1.6;
}

.btn {
  background-color: #4caf50; /* Green */
  border: none;
  color: rgb(215, 212, 212);
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
}
