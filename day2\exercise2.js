
if (typeof document !== "undefined") {
  document.getElementById("changeColor").addEventListener("click", function () {
    alert("Button clicked!");
    document.body.style.backgroundColor = "red";
  });
  console.log("Browser version loaded - click the button to change color!");
} else {
  // Node.js environment - simulate the functionality
  console.log("Node.js environment detected");
  console.log("Simulating button click event...");

  const simulateButtonClick = () => {
    console.log("Alert: Button clicked!");
    console.log("Background color changed to red");
  };

  // Call the function to simulate the click
  simulateButtonClick();
}
