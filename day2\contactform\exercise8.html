<!doctype html>
<html>
  <head>
    <title>Contact Form</title>
    <link rel="stylesheet" href="contactform-style.css" />
  </head>
  <body>
    <form id="contact-form">
      <input type="text" id="nama" name="nama" placeholder="Name" required />
      <input
        type="email"
        id="email"
        name="email"
        placeholder="Email"
        required
      />
      <button type="submit">Submit</button>
    </form>
    <p id="message"></p>
    <script>
      document
        .getElementById("contact-form")
        .addEventListener("submit", async function (e) {
          e.preventDefault();
          const nama = document.getElementById("nama").value;
          const email = document.getElementById("email").value;
          const message = document.getElementById("message");
          try {
            const res = await fetch("http://localhost:3001/api/contact", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ nama, email }),
            });
            const data = await res.json();
            if (res.ok) {
              message.textContent = data.message;
              message.style.color = "#28a745";
              document.getElementById("contact-form").reset();
            } else {
              message.textContent = data.message || "Submission failed.";
              message.style.color = "red";
            }
          } catch (err) {
            message.textContent = "Error connecting to server.";
            message.style.color = "red";
          }
        });
    </script>
  </body>
</html>
