<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tweet Length Challenge</title>
    <link rel="stylesheet" href="tweet.css" />
  </head>
  <body>
    <div class="container">
      <h2>Tweet Length Challenge</h2>
      <textarea
        id="tweetInput"
        maxlength="1000"
        placeholder="Type your tweet here..."
      ></textarea>
      <div class="info">
        <span id="charCount"></span><br />
        <span id="charLeft" class="characters-left"></span>
      </div>
    </div>
    <script>
      const tweetInput = document.getElementById("tweetInput");
      const charCount = document.getElementById("charCount");
      const charLeft = document.getElementById("charLeft");
      const tweetLimit = 1000;

      function updateCount() {
        const tweet = tweetInput.value;
        const tweetCount = tweet.length;
        const charactersLeft = tweetLimit - tweetCount;
        charCount.textContent = `You have written ${tweetCount} characters.`;
        charLeft.textContent = `You have ${charactersLeft} characters left.`;
      }

      tweetInput.addEventListener("input", updateCount);
      updateCount(); // Initial update
    </script>
  </body>
</html>
