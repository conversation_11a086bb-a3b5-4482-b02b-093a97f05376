// contactform-server.js
const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const bodyParser = require("body-parser");

const app = express();
app.use(cors());
app.use(bodyParser.json());

// Replace with your MongoDB connection string
const mongoURI =
  "mongodb+srv://Shafikanizam:<db_password>@project.i8rz2ma.mongodb.net/?retryWrites=true&w=majority&appName=Project";

mongoose
  .connect(mongoURI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log("MongoDB connected"))
  .catch((err) => console.error("MongoDB connection error:", err));

const ContactSchema = new mongoose.Schema({
  nama: String,
  email: String,
});
const Contact = mongoose.model("Contact", ContactSchema);

app.post("/api/contact", async (req, res) => {
  const { nama, email } = req.body;
  if (!nama || !email) {
    return res.status(400).json({ message: "Nama and email are required." });
  }
  try {
    const contact = new Contact({ nama, email });
    await contact.save();
    res.json({ message: "Contact saved successfully!" });
  } catch (err) {
    res.status(500).json({ message: "Error saving contact." });
  }
});

const PORT = 3001;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
