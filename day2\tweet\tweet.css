body {
  font-family: "Segoe UI", <PERSON><PERSON>, sans-serif;
  margin: 40px;
  background: linear-gradient(135deg, #f8ffae 0%, #43c6ac 100%);
}
.container {
  background: linear-gradient(120deg, #f093fb 0%, #f5576c 100%);
  padding: 30px;
  border-radius: 16px;
  max-width: 600px;
  margin: auto;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  color: #fff;
}
h2 {
  text-align: center;
  color: #fffbe7;
  text-shadow:
    1px 1px 8px #f5576c,
    0 0 2px #43c6ac;
}
textarea {
  width: 100%;
  height: 120px;
  font-size: 1.1rem;
  padding: 12px;
  border-radius: 8px;
  border: 2px solid #43c6ac;
  background: #fffbe7;
  color: #333;
  resize: vertical;
  box-shadow: 0 2px 8px rgba(67, 198, 172, 0.1);
  transition: border 0.2s;
}
textarea:focus {
  border: 2px solid #f5576c;
  outline: none;
}
.info {
  margin-top: 18px;
  font-size: 1.15rem;
  text-align: center;
}
.characters-left {
  color: #ffe53b;
  font-weight: bold;
  text-shadow: 0 1px 4px #f5576c;
}
#charCount {
  color: #fffbe7;
  text-shadow: 0 1px 4px #43c6ac;
}
